# Dify 服务器部署环境配置
# 请根据您的服务器环境修改以下配置

# ===========================================
# 重要：请修改以下配置以适应您的服务器
# ===========================================

# 您的服务器域名或IP地址（必须修改）
SERVER_DOMAIN=your-server-domain.com
# 或者使用IP地址：SERVER_DOMAIN=123.456.789.123

# 如果使用HTTPS，设置为 https，否则设置为 http
PROTOCOL=http

# ===========================================
# 应用密钥配置
# ===========================================

# 应用密钥 - 请生成一个强密钥替换
# 可以使用命令生成：openssl rand -base64 42
SECRET_KEY=************************************************

# ===========================================
# 服务URL配置（会根据上面的域名自动设置）
# ===========================================

# Console API base URL
CONSOLE_API_URL=${PROTOCOL}://${SERVER_DOMAIN}/console/api

# Console web base URL
CONSOLE_WEB_URL=${PROTOCOL}://${SERVER_DOMAIN}

# Service API base URL
SERVICE_API_URL=${PROTOCOL}://${SERVER_DOMAIN}/api

# Web APP base URL
APP_WEB_URL=${PROTOCOL}://${SERVER_DOMAIN}

# Files URL
FILES_URL=${PROTOCOL}://${SERVER_DOMAIN}/files

# ===========================================
# 数据库配置
# ===========================================

# PostgreSQL 数据库配置
DB_USERNAME=postgres
DB_PASSWORD=difyai123456_server
DB_HOST=db
DB_PORT=5432
DB_DATABASE=dify

# ===========================================
# Redis 配置
# ===========================================

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=difyai123456_server
REDIS_DB=0

# Celery 配置
CELERY_BROKER_URL=redis://:difyai123456_server@redis:6379/1

# Session 配置
SESSION_REDIS_HOST=redis
SESSION_REDIS_PORT=6379
SESSION_REDIS_PASSWORD=difyai123456_server
SESSION_REDIS_DB=2

# ===========================================
# 存储配置
# ===========================================

# 存储类型：local, s3, azure-blob, google-storage
STORAGE_TYPE=local
STORAGE_LOCAL_PATH=storage

# 如果使用 S3 存储，请配置以下选项
S3_ENDPOINT=
S3_BUCKET_NAME=
S3_ACCESS_KEY=
S3_SECRET_KEY=
S3_REGION=us-east-1

# ===========================================
# 向量数据库配置
# ===========================================

VECTOR_STORE=weaviate
WEAVIATE_ENDPOINT=http://weaviate:8080
WEAVIATE_API_KEY=WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih

# ===========================================
# 代码执行沙箱配置
# ===========================================

CODE_EXECUTION_ENDPOINT=http://sandbox:8194
CODE_EXECUTION_API_KEY=dify-sandbox

# ===========================================
# SSRF 防护配置
# ===========================================

SSRF_PROXY_HTTP_URL=http://ssrf_proxy:3128
SSRF_PROXY_HTTPS_URL=http://ssrf_proxy:3128

# ===========================================
# 邮件配置（可选）
# ===========================================

# 邮件类型：smtp, resend
MAIL_TYPE=smtp
MAIL_DEFAULT_SEND_FROM=noreply@${SERVER_DOMAIN}

# SMTP 配置
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true

# Resend 配置（如果使用 Resend）
RESEND_API_KEY=
RESEND_API_URL=https://api.resend.com

# ===========================================
# 文件上传配置
# ===========================================

UPLOAD_FILE_SIZE_LIMIT=15
UPLOAD_FILE_BATCH_LIMIT=5
UPLOAD_IMAGE_FILE_SIZE_LIMIT=10

# ===========================================
# 日志和调试配置
# ===========================================

LOG_LEVEL=INFO
DEBUG=false
FLASK_DEBUG=false

# ===========================================
# 数据库迁移配置
# ===========================================

MIGRATION_ENABLED=true

# ===========================================
# 索引配置
# ===========================================

INDEXING_MAX_SEGMENTATION_TOKENS_LENGTH=1000

# ===========================================
# 工作流配置
# ===========================================

WORKFLOW_MAX_EXECUTION_STEPS=500
WORKFLOW_MAX_EXECUTION_TIME=1200
WORKFLOW_CALL_MAX_DEPTH=5

# ===========================================
# 数据集配置
# ===========================================

CLEAN_DAY_SETTING=30

# ===========================================
# 多模态配置
# ===========================================

MULTIMODAL_SEND_IMAGE_FORMAT=base64

# ===========================================
# HTTP 请求配置
# ===========================================

HTTP_REQUEST_MAX_CONNECT_TIMEOUT=10
HTTP_REQUEST_MAX_READ_TIMEOUT=60
HTTP_REQUEST_MAX_WRITE_TIMEOUT=10
HTTP_REQUEST_NODE_MAX_BINARY_SIZE=10485760
HTTP_REQUEST_NODE_MAX_TEXT_SIZE=1048576

# ===========================================
# 监控和追踪配置（可选）
# ===========================================

OPS_TRACE_ENABLED=false
LANGFUSE_PUBLIC_KEY=
LANGFUSE_SECRET_KEY=
LANGFUSE_HOST=
LANGSMITH_API_KEY=
LANGSMITH_PROJECT=
TRACEABLE_API_KEY=
TRACEABLE_PROJECT=

# Sentry 错误监控（可选）
SENTRY_DSN=

# ===========================================
# 文件访问配置
# ===========================================

FILES_ACCESS_TIMEOUT=300

# ===========================================
# 默认用户配置
# ===========================================

# 初始管理员密码（留空则通过Web界面设置）
INIT_PASSWORD=

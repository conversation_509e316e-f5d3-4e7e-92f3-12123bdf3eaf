# Xinference 集成指南

本指南将帮助您在 Dify 项目中集成 Xinference，实现本地模型的部署和管理。

## 概述

Xinference 是一个强大的模型推理服务框架，支持多种类型的模型：
- **LLM (大语言模型)**: Qwen、ChatGLM、Baichuan 等
- **Embedding (嵌入模型)**: BGE、M3E、Text2Vec 等
- **Rerank (重排序模型)**: BGE-Reranker 等
- **图像模型**: SD、DALL-E 等

## 系统要求

### 硬件要求
- **CPU**: 4 核心以上推荐
- **内存**: 8GB 以上 (模型大小而定)
- **存储**: 50GB 以上可用空间
- **GPU**: 可选，支持 NVIDIA、AMD、Apple Silicon

### 软件要求
- **Python**: 3.8 或更高版本
- **操作系统**: Linux、macOS、Windows

## 快速开始

### 1. 安装 Xinference

```bash
# 自动安装脚本
./install_xinference.sh
```

安装脚本会自动：
- 检测操作系统和 Python 版本
- 检测 GPU 类型并安装相应版本
- 创建虚拟环境
- 安装 Xinference 和依赖
- 创建配置文件和目录

### 2. 启动 Xinference 服务

```bash
# 启动服务
./start_xinference.sh
```

服务启动后可以通过以下地址访问：
- **Web UI**: http://localhost:9997
- **API 文档**: http://localhost:9997/docs
- **API 端点**: http://localhost:9997/v1

### 3. 部署模型

#### 方法一：通过 Web UI
1. 访问 http://localhost:9997
2. 在 "Launch Model" 页面选择模型
3. 配置模型参数
4. 点击 "Launch" 部署模型

#### 方法二：通过 API
```bash
# 部署 Qwen2-7B-Instruct 模型
curl -X POST "http://localhost:9997/v1/models" \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "qwen2-instruct",
    "model_size_in_billions": 7
  }'

# 部署 BGE 嵌入模型
curl -X POST "http://localhost:9997/v1/models" \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "bge-base-zh-v1.5",
    "model_type": "embedding"
  }'
```

### 4. 在 Dify 中配置

#### 配置 LLM 模型
1. 登录 Dify 管理后台
2. 进入 "设置" -> "模型提供商"
3. 添加自定义模型提供商：
   - **提供商名称**: Xinference
   - **API 类型**: OpenAI 兼容
   - **API 端点**: http://localhost:9997/v1
   - **API 密钥**: 留空或设置自定义密钥

#### 配置 Embedding 模型
1. 在模型提供商中添加 Embedding 模型
2. 配置参数：
   - **模型名称**: bge-base-zh-v1.5
   - **API 端点**: http://localhost:9997/v1
   - **维度**: 768 (根据模型而定)

## 管理命令

### 服务管理
```bash
# 启动服务
./start_xinference.sh

# 停止服务
./stop_xinference.sh

# 重启服务
./restart_xinference.sh

# 检查状态
./check_xinference.sh
```

### 日志查看
```bash
# 实时查看日志
tail -f xinference_logs/xinference.log

# 查看错误日志
grep -i error xinference_logs/xinference.log
```

### 模型管理
```bash
# 列出已部署的模型
curl http://localhost:9997/v1/models

# 停止模型
curl -X DELETE http://localhost:9997/v1/models/{model_id}
```

## 推荐模型配置

### 中文场景推荐

#### LLM 模型
- **Qwen2-7B-Instruct**: 通用中文对话模型
- **ChatGLM3-6B**: 清华大学开源对话模型
- **Baichuan2-7B-Chat**: 百川智能对话模型

#### Embedding 模型
- **bge-base-zh-v1.5**: 中文文本嵌入
- **m3e-base**: 多语言嵌入模型
- **text2vec-base-chinese**: 中文语义向量

#### Rerank 模型
- **bge-reranker-base**: 中文重排序模型

### 英文场景推荐

#### LLM 模型
- **Llama-2-7B-Chat**: Meta 开源对话模型
- **Mistral-7B-Instruct**: Mistral AI 指令模型

#### Embedding 模型
- **bge-base-en-v1.5**: 英文文本嵌入
- **all-MiniLM-L6-v2**: 轻量级英文嵌入

## 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查日志
tail -f xinference_logs/xinference.log

# 检查端口占用
lsof -i :9997

# 检查虚拟环境
source xinference_env/bin/activate
which xinference-local
```

#### 2. 模型下载失败
- 检查网络连接
- 使用镜像源：设置 `HF_ENDPOINT=https://hf-mirror.com`
- 手动下载模型到 `xinference_models` 目录

#### 3. GPU 不可用
```bash
# 检查 NVIDIA GPU
nvidia-smi

# 检查 PyTorch GPU 支持
python -c "import torch; print(torch.cuda.is_available())"

# 重新安装 GPU 版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

#### 4. 内存不足
- 选择更小的模型
- 调整模型量化参数
- 增加系统内存或使用 GPU

### 性能优化

#### 1. GPU 加速
```bash
# 确保安装了正确的 PyTorch 版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

#### 2. 模型量化
在部署模型时使用量化参数：
```json
{
  "model_name": "qwen2-instruct",
  "model_size_in_billions": 7,
  "quantization": "int4"
}
```

#### 3. 并发设置
调整 Xinference 配置中的并发参数：
```yaml
# xinference_config/config.yaml
worker_ip: "0.0.0.0"
worker_port: 9997
max_workers: 4
```

## 高级配置

### 自定义模型
可以注册自定义模型到 Xinference：

```python
from xinference.model.llm import LLMModelSpec

# 定义自定义模型
custom_model = LLMModelSpec(
    model_name="custom-model",
    model_lang=["en", "zh"],
    model_ability=["generate", "chat"],
    model_family="custom",
    model_specs=[
        {
            "model_format": "pytorch",
            "model_size_in_billions": 7,
            "quantizations": ["none", "int4", "int8"],
            "model_id": "path/to/your/model"
        }
    ]
)

# 注册模型
from xinference.client import Client
client = Client("http://localhost:9997")
client.register_model(model_type="LLM", model=custom_model)
```

### 集群部署
Xinference 支持分布式部署：

```bash
# 启动 supervisor
xinference-supervisor -H 0.0.0.0 -p 9997

# 启动 worker
xinference-worker -e http://supervisor_host:9997
```

## 监控和维护

### 监控指标
- CPU/GPU 使用率
- 内存使用情况
- 模型推理延迟
- 请求成功率

### 定期维护
- 清理旧的模型缓存
- 更新 Xinference 版本
- 备份重要配置

## 支持和社区

- **官方文档**: https://inference.readthedocs.io/
- **GitHub**: https://github.com/xorbitsai/inference
- **问题反馈**: https://github.com/xorbitsai/inference/issues

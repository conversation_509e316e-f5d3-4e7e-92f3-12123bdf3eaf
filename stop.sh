#!/bin/bash

# Dify 停止脚本

# 添加 Docker 到 PATH（如果需要）
export PATH="/usr/local/bin:$PATH"

# 检查 Docker Compose 版本
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker compose"
elif command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker-compose"
else
    echo "❌ Docker Compose 未找到"
    exit 1
fi

echo "🛑 停止 Dify 服务..."

# 停止所有服务
$DOCKER_COMPOSE_CMD down

echo "✅ Dify 服务已停止"

# 询问是否清理数据
read -p "是否要清理所有数据？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  清理数据..."
    sudo rm -rf volumes/
    echo "✅ 数据已清理"
fi

echo "📋 其他操作："
echo "   重新启动: ./start.sh"
echo "   查看日志: $DOCKER_COMPOSE_CMD logs"
echo "   清理镜像: $DOCKER_COMPOSE_CMD down --rmi all"

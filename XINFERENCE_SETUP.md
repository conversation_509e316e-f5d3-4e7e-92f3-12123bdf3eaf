# Xinference 安装和集成完整指南

本指南将帮助您完成 Xinference 的安装、配置和与 Dify 的集成。

## 🚀 快速开始

### 第一步：安装 Xinference

```bash
# 运行自动安装脚本
./install_xinference.sh
```

这个脚本会：
- ✅ 检测您的操作系统 (macOS/Linux)
- ✅ 检查并安装 Python 3.8+
- ✅ 检测 GPU 类型 (NVIDIA/Apple Silicon/AMD/CPU)
- ✅ 创建虚拟环境
- ✅ 安装 Xinference 和相关依赖
- ✅ 创建配置文件和目录结构

### 第二步：启动 Xinference 服务

```bash
# 启动服务
./start_xinference.sh
```

服务启动后，您可以通过以下地址访问：
- **Web UI**: http://localhost:9997
- **API 文档**: http://localhost:9997/docs

### 第三步：部署模型

#### 方法一：通过 Web UI (推荐新手)
1. 打开 http://localhost:9997
2. 点击 "Launch Model"
3. 选择模型 (推荐: qwen2-instruct, bge-base-zh-v1.5)
4. 点击 "Launch" 开始部署

#### 方法二：通过命令行
```bash
# 部署中文对话模型 (7B 参数)
curl -X POST "http://localhost:9997/v1/models" \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "qwen2-instruct",
    "model_size_in_billions": 7
  }'

# 部署中文嵌入模型
curl -X POST "http://localhost:9997/v1/models" \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "bge-base-zh-v1.5",
    "model_type": "embedding"
  }'
```

### 第四步：配置 Dify 集成

1. **启动 Dify** (如果还没启动)
   ```bash
   ./start.sh
   ```

2. **访问 Dify 管理后台**
   打开 http://localhost

3. **添加模型提供商**
   - 进入 "设置" → "模型提供商"
   - 点击 "添加模型提供商"
   - 选择 "自定义"
   - 填写配置：
     ```
     提供商名称: Xinference
     API 类型: OpenAI 兼容
     API 端点: http://host.docker.internal:9997/v1
     API 密钥: sk-fake-key
     ```

4. **添加具体模型**
   - 在刚创建的提供商中点击 "添加模型"
   - 填写模型信息：
     ```
     模型名称: qwen2-instruct
     模型 ID: qwen2-instruct
     上下文长度: 32768
     ```

### 第五步：测试集成

```bash
# 运行集成测试
./test_integration.sh
```

## 📋 管理命令

### 服务管理
```bash
./start_xinference.sh    # 启动服务
./stop_xinference.sh     # 停止服务
./restart_xinference.sh  # 重启服务
./check_xinference.sh    # 检查状态
```

### 日志查看
```bash
# 实时查看 Xinference 日志
tail -f xinference_logs/xinference.log

# 查看 Dify 日志
docker-compose logs -f api
```

### 模型管理
```bash
# 列出已部署的模型
curl http://localhost:9997/v1/models

# 停止特定模型
curl -X DELETE http://localhost:9997/v1/models/{model_id}
```

## 🎯 推荐配置

### 入门配置 (4-8GB 内存)
```bash
# 小型对话模型
curl -X POST "http://localhost:9997/v1/models" -d '{
  "model_name": "qwen2-instruct",
  "model_size_in_billions": 1.5,
  "quantization": "q4_0"
}'

# 轻量级嵌入模型
curl -X POST "http://localhost:9997/v1/models" -d '{
  "model_name": "bge-small-zh-v1.5",
  "model_type": "embedding"
}'
```

### 标准配置 (16GB+ 内存)
```bash
# 标准对话模型
curl -X POST "http://localhost:9997/v1/models" -d '{
  "model_name": "qwen2-instruct",
  "model_size_in_billions": 7
}'

# 标准嵌入模型
curl -X POST "http://localhost:9997/v1/models" -d '{
  "model_name": "bge-base-zh-v1.5",
  "model_type": "embedding"
}'
```

### 高性能配置 (32GB+ 内存 + GPU)
```bash
# 大型对话模型
curl -X POST "http://localhost:9997/v1/models" -d '{
  "model_name": "qwen2-instruct",
  "model_size_in_billions": 14
}'

# 重排序模型
curl -X POST "http://localhost:9997/v1/models" -d '{
  "model_name": "bge-reranker-base",
  "model_type": "rerank"
}'
```

## 🔧 故障排除

### 常见问题

#### 1. 安装失败
```bash
# 检查 Python 版本
python3 --version

# 手动安装依赖
pip install xinference[all]
```

#### 2. 服务启动失败
```bash
# 检查端口占用
lsof -i :9997

# 查看详细日志
tail -f xinference_logs/xinference.log
```

#### 3. Dify 无法连接
```bash
# 测试网络连接
docker exec -it $(docker-compose ps -q api) curl http://host.docker.internal:9997/v1/models

# 检查防火墙设置
sudo ufw status  # Ubuntu
```

#### 4. 模型下载慢
```bash
# 设置镜像源
export HF_ENDPOINT=https://hf-mirror.com

# 重启 Xinference
./restart_xinference.sh
```

### 性能优化

#### GPU 加速
```bash
# 检查 GPU 状态
nvidia-smi  # NVIDIA GPU
system_profiler SPDisplaysDataType | grep Chipset  # Apple Silicon

# 重新安装 GPU 版本
source xinference_env/bin/activate
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

#### 内存优化
- 使用量化模型 (q4_0, q8_0)
- 选择合适大小的模型
- 调整并发设置

## 📚 详细文档

- **完整安装指南**: `xinference/README.md`
- **Dify 集成配置**: `dify_xinference_integration.md`
- **API 文档**: http://localhost:9997/docs (服务启动后)

## 🆘 获取帮助

### 检查系统状态
```bash
# 全面状态检查
./check_xinference.sh

# 测试集成
./test_integration.sh
```

### 日志分析
```bash
# Xinference 错误日志
grep -i error xinference_logs/xinference.log

# Dify 连接日志
docker-compose logs api | grep -i xinference
```

### 重置环境
```bash
# 停止所有服务
./stop_xinference.sh
docker-compose down

# 清理并重新开始
rm -rf xinference_env xinference_models xinference_logs
./install_xinference.sh
```

## 🎉 成功标志

当您看到以下内容时，说明集成成功：

1. ✅ Xinference Web UI 可访问: http://localhost:9997
2. ✅ 模型成功部署并显示在模型列表中
3. ✅ Dify 中可以选择和使用 Xinference 模型
4. ✅ 测试对话正常响应

恭喜！您已经成功完成了 Xinference 和 Dify 的集成配置。

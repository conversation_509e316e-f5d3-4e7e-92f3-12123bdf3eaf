#!/bin/bash

# Xinference 停止脚本

echo "🛑 停止 Xinference 服务..."

# 检查 PID 文件
if [ -f "xinference_logs/xinference.pid" ]; then
    PID=$(cat xinference_logs/xinference.pid)
    echo "📋 从 PID 文件读取进程 ID: $PID"
    
    # 检查进程是否存在
    if ps -p $PID > /dev/null 2>&1; then
        echo "🔄 正在停止进程 $PID..."
        kill $PID
        
        # 等待进程结束
        for i in {1..10}; do
            if ! ps -p $PID > /dev/null 2>&1; then
                echo "✅ 进程已停止"
                break
            fi
            echo "等待进程结束... ($i/10)"
            sleep 1
        done
        
        # 如果进程仍在运行，强制杀死
        if ps -p $PID > /dev/null 2>&1; then
            echo "⚠️  强制停止进程..."
            kill -9 $PID
            sleep 2
        fi
    else
        echo "⚠️  PID 文件中的进程不存在"
    fi
    
    # 删除 PID 文件
    rm -f xinference_logs/xinference.pid
fi

# 查找并停止所有 xinference 进程
echo "🔍 查找其他 Xinference 进程..."
PIDS=$(pgrep -f "xinference" 2>/dev/null || true)

if [ -n "$PIDS" ]; then
    echo "📋 找到 Xinference 进程: $PIDS"
    for pid in $PIDS; do
        echo "🔄 停止进程 $pid..."
        kill $pid 2>/dev/null || true
    done
    
    # 等待进程结束
    sleep 3
    
    # 检查是否还有进程在运行
    REMAINING_PIDS=$(pgrep -f "xinference" 2>/dev/null || true)
    if [ -n "$REMAINING_PIDS" ]; then
        echo "⚠️  强制停止剩余进程: $REMAINING_PIDS"
        for pid in $REMAINING_PIDS; do
            kill -9 $pid 2>/dev/null || true
        done
    fi
fi

# 检查端口是否仍被占用
if lsof -Pi :9997 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  端口 9997 仍被占用"
    PID=$(lsof -Pi :9997 -sTCP:LISTEN -t)
    echo "占用进程 PID: $PID"
    echo "进程信息:"
    ps -p $PID 2>/dev/null || echo "无法获取进程信息"
else
    echo "✅ 端口 9997 已释放"
fi

echo "✅ Xinference 服务已停止"
echo ""
echo "📋 其他操作："
echo "   重新启动: ./start_xinference.sh"
echo "   查看日志: tail -f xinference_logs/xinference.log"
echo "   检查状态: ./check_xinference.sh"

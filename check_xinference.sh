#!/bin/bash

# Xinference 状态检查脚本

echo "🔍 检查 Xinference 服务状态..."

# 检查进程
echo "📋 检查进程状态..."
PIDS=$(pgrep -f "xinference" 2>/dev/null || true)

if [ -n "$PIDS" ]; then
    echo "✅ Xinference 进程正在运行:"
    for pid in $PIDS; do
        echo "   PID: $pid"
        ps -p $pid -o pid,ppid,cmd --no-headers 2>/dev/null || echo "   无法获取进程详情"
    done
else
    echo "❌ 未找到 Xinference 进程"
fi

# 检查端口
echo ""
echo "🔍 检查端口状态..."
if lsof -Pi :9997 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "✅ 端口 9997 正在监听"
    PID=$(lsof -Pi :9997 -sTCP:LISTEN -t)
    PROCESS_NAME=$(ps -p $PID -o comm= 2>/dev/null || echo "unknown")
    echo "   占用进程: $PROCESS_NAME (PID: $PID)"
else
    echo "❌ 端口 9997 未被监听"
fi

# 检查 HTTP 服务
echo ""
echo "🌐 检查 HTTP 服务..."
if curl -f -s http://localhost:9997/docs > /dev/null 2>&1; then
    echo "✅ HTTP 服务正常响应"
    
    # 获取服务信息
    echo ""
    echo "📊 服务信息:"
    if command -v curl &> /dev/null && command -v jq &> /dev/null; then
        # 尝试获取版本信息
        VERSION=$(curl -s http://localhost:9997/v1/cluster/version 2>/dev/null | jq -r '.version' 2>/dev/null || echo "未知")
        echo "   版本: $VERSION"
        
        # 尝试获取模型列表
        MODELS=$(curl -s http://localhost:9997/v1/models 2>/dev/null | jq -r '.data[].id' 2>/dev/null | wc -l || echo "0")
        echo "   已部署模型数量: $MODELS"
    else
        echo "   (需要安装 jq 来显示详细信息)"
    fi
    
    echo ""
    echo "🔗 访问地址:"
    echo "   Web UI: http://localhost:9997"
    echo "   API 文档: http://localhost:9997/docs"
    echo "   API 端点: http://localhost:9997/v1"
    
else
    echo "❌ HTTP 服务无响应"
fi

# 检查日志文件
echo ""
echo "📝 日志文件状态..."
if [ -f "xinference_logs/xinference.log" ]; then
    LOG_SIZE=$(du -h xinference_logs/xinference.log | cut -f1)
    LOG_LINES=$(wc -l < xinference_logs/xinference.log)
    echo "✅ 日志文件存在"
    echo "   文件大小: $LOG_SIZE"
    echo "   行数: $LOG_LINES"
    
    # 显示最新的错误日志
    echo ""
    echo "🔍 最近的错误日志:"
    grep -i "error\|exception\|failed" xinference_logs/xinference.log | tail -3 2>/dev/null || echo "   无错误日志"
    
    echo ""
    echo "📝 最新日志 (最后 5 行):"
    tail -5 xinference_logs/xinference.log 2>/dev/null || echo "   无法读取日志"
else
    echo "❌ 日志文件不存在"
fi

# 检查配置文件
echo ""
echo "⚙️  配置文件状态..."
if [ -f "xinference_config/config.yaml" ]; then
    echo "✅ 配置文件存在"
else
    echo "❌ 配置文件不存在"
fi

# 检查模型目录
echo ""
echo "📁 模型目录状态..."
if [ -d "xinference_models" ]; then
    MODEL_COUNT=$(find xinference_models -name "*.bin" -o -name "*.safetensors" -o -name "*.gguf" 2>/dev/null | wc -l)
    MODEL_SIZE=$(du -sh xinference_models 2>/dev/null | cut -f1 || echo "0B")
    echo "✅ 模型目录存在"
    echo "   模型文件数量: $MODEL_COUNT"
    echo "   目录大小: $MODEL_SIZE"
else
    echo "❌ 模型目录不存在"
fi

# 检查虚拟环境
echo ""
echo "🐍 虚拟环境状态..."
if [ -d "xinference_env" ]; then
    echo "✅ 虚拟环境存在"
    if [ -f "xinference_env/bin/xinference-local" ]; then
        echo "   Xinference 可执行文件存在"
    else
        echo "   ❌ Xinference 可执行文件不存在"
    fi
else
    echo "❌ 虚拟环境不存在"
fi

echo ""
echo "📋 管理命令:"
echo "   启动服务: ./start_xinference.sh"
echo "   停止服务: ./stop_xinference.sh"
echo "   重启服务: ./restart_xinference.sh"
echo "   查看日志: tail -f xinference_logs/xinference.log"

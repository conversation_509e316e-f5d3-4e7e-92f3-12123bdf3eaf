#!/bin/bash

# Xinference 安装脚本
# 支持 macOS 和 Linux 系统

set -e

echo "🚀 开始安装 Xinference..."

# 检测操作系统
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

echo "📱 检测到操作系统: $MACHINE"

# 检查 Python 版本
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        echo "🐍 检测到 Python 版本: $PYTHON_VERSION"
        
        # 检查版本是否 >= 3.8
        if python3 -c 'import sys; exit(0 if sys.version_info >= (3, 8) else 1)'; then
            echo "✅ Python 版本满足要求 (>= 3.8)"
            return 0
        else
            echo "❌ Python 版本过低，需要 >= 3.8"
            return 1
        fi
    else
        echo "❌ 未找到 Python3，请先安装 Python 3.8+"
        return 1
    fi
}

# 安装 Python (macOS)
install_python_mac() {
    echo "📦 在 macOS 上安装 Python..."
    if command -v brew &> /dev/null; then
        brew install python@3.11
    else
        echo "❌ 未找到 Homebrew，请先安装 Homebrew 或手动安装 Python 3.8+"
        echo "   Homebrew 安装: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
}

# 安装 Python (Linux)
install_python_linux() {
    echo "📦 在 Linux 上安装 Python..."
    if command -v apt-get &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y python3 python3-pip python3-venv
    elif command -v yum &> /dev/null; then
        sudo yum install -y python3 python3-pip
    elif command -v dnf &> /dev/null; then
        sudo dnf install -y python3 python3-pip
    else
        echo "❌ 不支持的 Linux 发行版，请手动安装 Python 3.8+"
        exit 1
    fi
}

# 检查并安装 Python
if ! check_python; then
    echo "🔧 开始安装 Python..."
    case $MACHINE in
        Mac)
            install_python_mac
            ;;
        Linux)
            install_python_linux
            ;;
        *)
            echo "❌ 不支持的操作系统: $MACHINE"
            exit 1
            ;;
    esac
    
    # 重新检查
    if ! check_python; then
        echo "❌ Python 安装失败"
        exit 1
    fi
fi

# 检查 GPU 支持
check_gpu() {
    echo "🔍 检查 GPU 支持..."
    
    # 检查 NVIDIA GPU
    if command -v nvidia-smi &> /dev/null; then
        echo "✅ 检测到 NVIDIA GPU"
        nvidia-smi --query-gpu=name --format=csv,noheader
        GPU_TYPE="nvidia"
        return 0
    fi
    
    # 检查 Apple Silicon (M1/M2)
    if [[ "$MACHINE" == "Mac" ]] && [[ "$(uname -m)" == "arm64" ]]; then
        echo "✅ 检测到 Apple Silicon (M1/M2)"
        GPU_TYPE="mps"
        return 0
    fi
    
    # 检查 AMD GPU (Linux)
    if [[ "$MACHINE" == "Linux" ]] && command -v rocm-smi &> /dev/null; then
        echo "✅ 检测到 AMD GPU"
        GPU_TYPE="rocm"
        return 0
    fi
    
    echo "⚠️  未检测到 GPU，将使用 CPU 模式"
    GPU_TYPE="cpu"
    return 0
}

check_gpu

# 创建虚拟环境
echo "📁 创建 Xinference 虚拟环境..."
python3 -m venv xinference_env

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source xinference_env/bin/activate

# 升级 pip
echo "⬆️  升级 pip..."
pip install --upgrade pip

# 根据 GPU 类型安装相应版本
echo "📦 安装 Xinference..."
case $GPU_TYPE in
    nvidia)
        echo "🎮 安装 NVIDIA GPU 版本..."
        pip install "xinference[all]"
        # 安装 CUDA 版本的 PyTorch
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
        ;;
    mps)
        echo "🍎 安装 Apple Silicon 版本..."
        pip install "xinference[all]"
        # PyTorch 对 Apple Silicon 的支持
        pip install torch torchvision torchaudio
        ;;
    rocm)
        echo "🔴 安装 AMD GPU 版本..."
        pip install "xinference[all]"
        # 安装 ROCm 版本的 PyTorch
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/rocm5.4.2
        ;;
    cpu)
        echo "💻 安装 CPU 版本..."
        pip install "xinference[all]"
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
        ;;
esac

# 创建配置目录
echo "📁 创建配置目录..."
mkdir -p xinference_config
mkdir -p xinference_logs
mkdir -p xinference_models

# 创建配置文件
echo "📝 创建配置文件..."
cat > xinference_config/config.yaml << EOF
# Xinference 配置文件
host: "0.0.0.0"
port: 9997
model_cache_dir: "./xinference_models"
log_level: "INFO"
auth_config:
  auth_keys: []
  
# GPU 配置
gpu_config:
  type: "$GPU_TYPE"
  
# 模型配置
model_config:
  # 默认下载的模型
  default_models:
    - name: "qwen2-instruct"
      model_size_in_billions: 7
    - name: "bge-base-zh-v1.5"
      model_type: "embedding"
EOF

echo "✅ Xinference 安装完成！"
echo ""
echo "📋 安装信息："
echo "   虚拟环境: ./xinference_env"
echo "   配置文件: ./xinference_config/config.yaml"
echo "   模型目录: ./xinference_models"
echo "   日志目录: ./xinference_logs"
echo "   GPU 类型: $GPU_TYPE"
echo ""
echo "🚀 下一步："
echo "   1. 启动 Xinference: ./start_xinference.sh"
echo "   2. 查看状态: ./check_xinference.sh"
echo "   3. 配置 Dify 集成"
echo ""
echo "📖 更多信息请查看: xinference/README.md"

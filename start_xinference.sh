#!/bin/bash

# Xinference 启动脚本

set -e

echo "🚀 启动 Xinference 服务..."

# 检查虚拟环境是否存在
if [ ! -d "xinference_env" ]; then
    echo "❌ Xinference 虚拟环境不存在，请先运行: ./install_xinference.sh"
    exit 1
fi

# 检查配置文件是否存在
if [ ! -f "xinference_config/config.yaml" ]; then
    echo "❌ 配置文件不存在，请先运行: ./install_xinference.sh"
    exit 1
fi

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source xinference_env/bin/activate

# 检查端口是否被占用
echo "🔍 检查端口 9997 是否被占用..."
if lsof -Pi :9997 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  端口 9997 已被占用"
    echo "正在检查是否为 Xinference 进程..."
    
    PID=$(lsof -Pi :9997 -sTCP:LISTEN -t)
    PROCESS_NAME=$(ps -p $PID -o comm= 2>/dev/null || echo "unknown")
    
    if [[ "$PROCESS_NAME" == *"xinference"* ]] || [[ "$PROCESS_NAME" == *"python"* ]]; then
        echo "✅ Xinference 服务已在运行 (PID: $PID)"
        echo "📱 访问地址: http://localhost:9997"
        echo "📋 查看状态: ./check_xinference.sh"
        echo "🛑 停止服务: ./stop_xinference.sh"
        exit 0
    else
        echo "❌ 端口被其他进程占用: $PROCESS_NAME (PID: $PID)"
        echo "请停止该进程或修改 Xinference 端口配置"
        exit 1
    fi
fi

# 创建日志目录
mkdir -p xinference_logs

# 启动 Xinference 服务
echo "🚀 启动 Xinference 服务..."
echo "📁 工作目录: $(pwd)"
echo "📝 日志文件: xinference_logs/xinference.log"

# 后台启动服务
nohup xinference-local \
    --host 0.0.0.0 \
    --port 9997 \
    --model-dir ./xinference_models \
    > xinference_logs/xinference.log 2>&1 &

XINFERENCE_PID=$!
echo $XINFERENCE_PID > xinference_logs/xinference.pid

echo "✅ Xinference 服务启动中..."
echo "📋 进程 ID: $XINFERENCE_PID"

# 等待服务启动
echo "⏳ 等待服务启动..."
for i in {1..30}; do
    if curl -f http://localhost:9997/docs &> /dev/null; then
        echo "✅ Xinference 服务启动成功！"
        echo ""
        echo "🎉 服务信息："
        echo "   📱 Web UI: http://localhost:9997"
        echo "   📖 API 文档: http://localhost:9997/docs"
        echo "   🔗 API 端点: http://localhost:9997/v1"
        echo ""
        echo "📋 管理命令："
        echo "   查看状态: ./check_xinference.sh"
        echo "   查看日志: tail -f xinference_logs/xinference.log"
        echo "   停止服务: ./stop_xinference.sh"
        echo "   重启服务: ./restart_xinference.sh"
        echo ""
        echo "🔧 下一步："
        echo "   1. 访问 Web UI 下载和部署模型"
        echo "   2. 在 Dify 中配置自定义模型提供商"
        echo "   3. 测试模型连接"
        exit 0
    fi
    echo "等待服务启动... ($i/30)"
    sleep 2
done

echo "⚠️  服务启动可能需要更长时间"
echo "📋 检查方法："
echo "   查看日志: tail -f xinference_logs/xinference.log"
echo "   检查进程: ps aux | grep xinference"
echo "   访问地址: http://localhost:9997"

# 显示日志的最后几行
echo ""
echo "📝 最新日志："
tail -n 10 xinference_logs/xinference.log 2>/dev/null || echo "日志文件尚未生成"

# Dify 本地部署指南

这个项目包含了使用 Docker Compose 在本地部署 Dify 的完整配置。

## 系统要求

- **CPU**: >= 2 Core
- **内存**: >= 4 GiB
- **存储**: >= 10 GiB 可用空间
- **操作系统**: Linux, macOS, 或 Windows (with WSL2)

## 软件要求

- Docker 19.03 或更高版本
- Docker Compose 1.28 或更高版本

### 安装 Docker (Ubuntu/Debian)

```bash
# 更新包索引
sudo apt update

# 安装必要的包
sudo apt install apt-transport-https ca-certificates curl gnupg lsb-release

# 添加 Docker 官方 GPG 密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加 Docker 仓库
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装 Docker
sudo apt update
sudo apt install docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到 docker 组
sudo usermod -aG docker $USER
```

### 安装 Docker (macOS)

下载并安装 [Docker Desktop for Mac](https://docs.docker.com/desktop/mac/install/)

### 安装 Docker (Windows)

下载并安装 [Docker Desktop for Windows](https://docs.docker.com/desktop/windows/install/)

## 快速开始

### 1. 克隆或下载项目文件

确保您有以下文件结构：

```
dify-local/
├── docker-compose.yml
├── .env
├── start.sh
├── stop.sh
├── README.md
├── nginx/
│   ├── nginx.conf
│   ├── proxy.conf
│   └── conf.d/
│       └── default.conf
└── ssrf_proxy/
    └── squid.conf
```

### 2. 设置执行权限

```bash
chmod +x start.sh stop.sh
```

### 3. 启动 Dify

```bash
./start.sh
```

或者手动启动：

```bash
# 创建数据目录
mkdir -p volumes/{app/storage,db/data,redis/data,weaviate,sandbox/dependencies}

# 启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 4. 访问 Dify

- **管理后台**: http://localhost
- **首次设置**: http://localhost/install
- **API 文档**: http://localhost/console/api/docs

## 服务组件

Dify 包含以下服务组件：

- **api**: Dify API 服务
- **worker**: Celery 工作进程
- **web**: 前端 Web 应用
- **db**: PostgreSQL 数据库
- **redis**: Redis 缓存
- **weaviate**: 向量数据库
- **sandbox**: 代码执行沙箱
- **ssrf_proxy**: SSRF 防护代理
- **nginx**: 反向代理服务器

## 常用命令

### 查看服务状态
```bash
docker-compose ps
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f api
docker-compose logs -f web
```

### 重启服务
```bash
docker-compose restart
```

### 停止服务
```bash
./stop.sh
# 或
docker-compose down
```

### 更新服务
```bash
docker-compose pull
docker-compose up -d
```

## 配置说明

### 环境变量配置

主要配置在 `.env` 文件中：

- **SECRET_KEY**: 应用密钥，用于会话加密
- **数据库配置**: PostgreSQL 连接信息
- **Redis 配置**: Redis 连接信息
- **存储配置**: 文件存储方式（本地/云存储）
- **向量数据库**: Weaviate 配置

### 自定义配置

1. **修改端口**: 在 `docker-compose.yml` 中修改 nginx 的端口映射
2. **存储配置**: 修改 `.env` 中的 `STORAGE_TYPE` 等配置
3. **邮件配置**: 配置 SMTP 或 Resend 邮件服务
4. **向量数据库**: 可选择 Weaviate、Qdrant、Milvus 等

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :80
   # 修改 docker-compose.yml 中的端口映射
   ```

2. **权限问题**
   ```bash
   # 设置目录权限
   sudo chown -R $USER:$USER volumes/
   chmod -R 755 volumes/
   ```

3. **内存不足**
   - 确保系统有至少 4GB 可用内存
   - 可以调整 Docker 的内存限制

4. **服务启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs
   
   # 重新构建并启动
   docker-compose down
   docker-compose up -d --force-recreate
   ```

### 数据备份

```bash
# 备份数据
tar -czf dify-backup-$(date +%Y%m%d).tar.gz volumes/

# 恢复数据
tar -xzf dify-backup-YYYYMMDD.tar.gz
```

## 升级指南

1. 备份数据
2. 停止服务
3. 拉取新镜像
4. 启动服务

```bash
# 备份
tar -czf dify-backup-$(date +%Y%m%d).tar.gz volumes/

# 停止服务
docker-compose down

# 拉取新镜像
docker-compose pull

# 启动服务
docker-compose up -d
```

## 安全建议

1. **修改默认密码**: 更改 `.env` 中的数据库密码和 API 密钥
2. **网络安全**: 如果暴露到公网，建议配置 HTTPS 和防火墙
3. **定期备份**: 定期备份数据库和文件存储
4. **监控日志**: 定期检查应用日志

## 支持

- [Dify 官方文档](https://docs.dify.ai/)
- [GitHub Issues](https://github.com/langgenius/dify/issues)
- [社区论坛](https://github.com/langgenius/dify/discussions)

## Xinference 集成

本项目支持集成 Xinference 来部署和管理本地模型。

### 安装 Xinference

```bash
# 安装 Xinference
./install_xinference.sh

# 启动 Xinference 服务
./start_xinference.sh

# 查看 Xinference 状态
./check_xinference.sh
```

### 配置 Dify 使用 Xinference

1. 启动 Xinference 服务
2. 在 Dify 管理后台中添加自定义模型提供商
3. 配置 Xinference 的 API 端点和模型

详细配置说明请参考 `xinference/README.md`

## 许可证

本项目遵循 Dify 的开源许可证。

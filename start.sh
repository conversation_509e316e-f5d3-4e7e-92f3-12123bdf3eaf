#!/bin/bash

# Dify 本地部署启动脚本

echo "🚀 开始部署 Dify..."

# 添加 Docker 到 PATH（如果需要）
export PATH="/usr/local/bin:$PATH"

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查 Docker Compose 是否安装（优先使用 V2）
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker compose"
elif command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker-compose"
else
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

echo "✅ 使用 Docker Compose: $DOCKER_COMPOSE_CMD"

# 创建必要的目录
echo "📁 创建数据目录..."
mkdir -p volumes/app/storage
mkdir -p volumes/db/data
mkdir -p volumes/redis/data
mkdir -p volumes/weaviate
mkdir -p volumes/sandbox/dependencies

# 设置目录权限
echo "🔐 设置目录权限..."
chmod -R 755 volumes/

# 检查端口是否被占用
echo "🔍 检查端口占用情况..."
if lsof -Pi :80 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  警告: 端口 80 已被占用，请确保没有其他服务使用此端口"
    read -p "是否继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 拉取最新镜像
echo "📦 拉取 Docker 镜像..."
$DOCKER_COMPOSE_CMD pull

# 启动服务
echo "🚀 启动 Dify 服务..."
$DOCKER_COMPOSE_CMD up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
$DOCKER_COMPOSE_CMD ps

# 检查服务健康状态
echo "🏥 检查服务健康状态..."
for i in {1..30}; do
    if curl -f http://localhost/health &> /dev/null; then
        echo "✅ Dify 服务已成功启动！"
        echo ""
        echo "🎉 部署完成！"
        echo ""
        echo "📱 访问地址："
        echo "   管理后台: http://localhost"
        echo "   API 文档: http://localhost/console/api/docs"
        echo ""
        echo "🔧 首次使用请访问: http://localhost/install"
        echo "   设置管理员账户"
        echo ""
        echo "📋 查看日志: $DOCKER_COMPOSE_CMD logs -f"
        echo "🛑 停止服务: $DOCKER_COMPOSE_CMD down"
        echo "🔄 重启服务: $DOCKER_COMPOSE_CMD restart"
        exit 0
    fi
    echo "等待服务启动... ($i/30)"
    sleep 10
done

echo "⚠️  服务启动可能需要更长时间，请手动检查："
echo "   $DOCKER_COMPOSE_CMD logs -f"
echo "   访问 http://localhost 查看是否可用"

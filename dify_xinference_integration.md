# Dify + Xinference 集成配置指南

本指南详细说明如何将 Xinference 部署的本地模型集成到 Dify 中。

## 集成架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│  Dify (Docker)  │───▶│  Host Network   │───▶│   Xinference    │
│                 │    │                 │    │   (Host/Local)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
      :80                   :9997                   Models
```

## 步骤一：部署 Xinference

### 1. 安装 Xinference
```bash
# 运行安装脚本
./install_xinference.sh

# 启动服务
./start_xinference.sh

# 检查状态
./check_xinference.sh
```

### 2. 部署推荐模型

#### 中文 LLM 模型
```bash
# 通过 API 部署 Qwen2-7B
curl -X POST "http://localhost:9997/v1/models" \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "qwen2-instruct",
    "model_size_in_billions": 7,
    "model_format": "ggmlv3",
    "quantization": "q4_0"
  }'
```

#### 中文 Embedding 模型
```bash
# 部署 BGE 嵌入模型
curl -X POST "http://localhost:9997/v1/models" \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "bge-base-zh-v1.5",
    "model_type": "embedding"
  }'
```

#### Rerank 模型
```bash
# 部署重排序模型
curl -X POST "http://localhost:9997/v1/models" \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "bge-reranker-base",
    "model_type": "rerank"
  }'
```

## 步骤二：配置 Dify

### 1. 启动 Dify
```bash
# 确保 Dify 正在运行
./start.sh
```

### 2. 访问 Dify 管理后台
打开浏览器访问：http://localhost

### 3. 配置模型提供商

#### 添加 LLM 提供商
1. 进入 "设置" -> "模型提供商"
2. 点击 "添加模型提供商"
3. 选择 "自定义"
4. 填写配置：
   ```
   提供商名称: Xinference-LLM
   API 类型: OpenAI 兼容
   API 端点: http://host.docker.internal:9997/v1
   API 密钥: sk-fake-key (可以是任意值)
   ```

#### 添加 Embedding 提供商
1. 添加另一个自定义提供商
2. 填写配置：
   ```
   提供商名称: Xinference-Embedding
   API 类型: OpenAI 兼容
   API 端点: http://host.docker.internal:9997/v1
   API 密钥: sk-fake-key
   ```

### 4. 配置具体模型

#### 配置 LLM 模型
1. 在 LLM 提供商中点击 "添加模型"
2. 填写模型信息：
   ```
   模型名称: qwen2-instruct
   模型 ID: qwen2-instruct (与 Xinference 中的模型名一致)
   上下文长度: 32768
   最大输出长度: 8192
   支持流式输出: 是
   支持函数调用: 是 (如果模型支持)
   ```

#### 配置 Embedding 模型
1. 在 Embedding 提供商中点击 "添加模型"
2. 填写模型信息：
   ```
   模型名称: bge-base-zh-v1.5
   模型 ID: bge-base-zh-v1.5
   向量维度: 768
   最大输入长度: 512
   ```

## 步骤三：网络配置

### Docker 网络配置

由于 Dify 运行在 Docker 容器中，需要配置网络以访问宿主机的 Xinference 服务。

#### 方法一：使用 host.docker.internal (推荐)
在 Dify 配置中使用：
```
API 端点: http://host.docker.internal:9997/v1
```

#### 方法二：修改 Docker Compose 网络
如果方法一不工作，可以修改 `docker-compose.yml`：

```yaml
services:
  api:
    # ... 其他配置
    extra_hosts:
      - "xinference-host:host-gateway"
    # ... 其他配置
```

然后在 Dify 中使用：
```
API 端点: http://xinference-host:9997/v1
```

#### 方法三：使用宿主机 IP
获取宿主机 IP 并在 Dify 中配置：
```bash
# 获取宿主机 IP (Linux)
ip route show default | awk '/default/ {print $3}'

# 获取宿主机 IP (macOS)
route -n get default | grep gateway | awk '{print $2}'
```

## 步骤四：测试集成

### 1. 测试 LLM 模型
```bash
# 通过 Xinference API 测试
curl -X POST "http://localhost:9997/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "qwen2-instruct",
    "messages": [
      {"role": "user", "content": "你好，请介绍一下自己"}
    ],
    "stream": false
  }'
```

### 2. 测试 Embedding 模型
```bash
# 通过 Xinference API 测试
curl -X POST "http://localhost:9997/v1/embeddings" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "bge-base-zh-v1.5",
    "input": "这是一个测试文本"
  }'
```

### 3. 在 Dify 中测试
1. 创建一个新的应用
2. 选择刚配置的 Xinference 模型
3. 发送测试消息验证功能

## 故障排除

### 常见问题

#### 1. 连接超时
**问题**: Dify 无法连接到 Xinference
**解决方案**:
- 检查 Xinference 是否正在运行：`./check_xinference.sh`
- 检查防火墙设置
- 验证网络配置

#### 2. 模型未找到
**问题**: 提示模型不存在
**解决方案**:
- 确认模型已在 Xinference 中部署
- 检查模型名称是否一致
- 查看 Xinference 日志

#### 3. 性能问题
**问题**: 响应速度慢
**解决方案**:
- 使用量化模型减少内存使用
- 启用 GPU 加速
- 调整并发参数

### 调试命令

```bash
# 检查 Xinference 状态
./check_xinference.sh

# 查看 Xinference 日志
tail -f xinference_logs/xinference.log

# 查看 Dify 日志
docker-compose logs -f api

# 测试网络连接
docker exec -it $(docker-compose ps -q api) curl http://host.docker.internal:9997/v1/models
```

## 性能优化建议

### 1. 硬件优化
- 使用 SSD 存储模型文件
- 增加内存容量
- 使用 GPU 加速推理

### 2. 模型选择
- 根据需求选择合适大小的模型
- 使用量化模型减少资源消耗
- 考虑使用专门的中文模型

### 3. 配置优化
- 调整 Xinference 的并发设置
- 配置合适的上下文长度
- 启用模型缓存

## 监控和维护

### 1. 监控指标
- Xinference 服务状态
- 模型推理延迟
- 资源使用情况
- 错误率统计

### 2. 定期维护
- 更新 Xinference 版本
- 清理模型缓存
- 备份配置文件
- 检查日志文件大小

## 扩展配置

### 多模型部署
可以同时部署多个模型以支持不同场景：

```bash
# 部署不同大小的模型
curl -X POST "http://localhost:9997/v1/models" -d '{"model_name": "qwen2-instruct", "model_size_in_billions": 1.5}'
curl -X POST "http://localhost:9997/v1/models" -d '{"model_name": "qwen2-instruct", "model_size_in_billions": 7}'
curl -X POST "http://localhost:9997/v1/models" -d '{"model_name": "qwen2-instruct", "model_size_in_billions": 14}'
```

### 负载均衡
对于高并发场景，可以部署多个 Xinference 实例：

```bash
# 启动多个 Xinference 实例
xinference-local --host 0.0.0.0 --port 9997 &
xinference-local --host 0.0.0.0 --port 9998 &
xinference-local --host 0.0.0.0 --port 9999 &
```

然后在 Dify 中配置多个提供商或使用负载均衡器。

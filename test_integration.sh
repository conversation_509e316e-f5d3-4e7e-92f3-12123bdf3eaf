#!/bin/bash

# Dify + Xinference 集成测试脚本

set -e

echo "🧪 开始测试 Dify + Xinference 集成..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_xinference_service() {
    echo "🔍 测试 Xinference 服务..."
    
    if curl -f -s http://localhost:9997/docs > /dev/null; then
        echo -e "${GREEN}✅ Xinference 服务正常${NC}"
        return 0
    else
        echo -e "${RED}❌ Xinference 服务不可用${NC}"
        return 1
    fi
}

test_dify_service() {
    echo "🔍 测试 Dify 服务..."
    
    if curl -f -s http://localhost/health > /dev/null; then
        echo -e "${GREEN}✅ Dify 服务正常${NC}"
        return 0
    else
        echo -e "${RED}❌ Dify 服务不可用${NC}"
        return 1
    fi
}

test_network_connectivity() {
    echo "🔍 测试网络连接..."
    
    # 从 Dify 容器内测试连接到 Xinference
    if docker exec -it $(docker-compose ps -q api 2>/dev/null) curl -f -s http://host.docker.internal:9997/v1/models > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Dify 可以访问 Xinference${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  Dify 无法通过 host.docker.internal 访问 Xinference${NC}"
        echo "尝试其他网络配置..."
        return 1
    fi
}

deploy_test_model() {
    echo "🚀 部署测试模型..."
    
    # 部署一个小型的测试模型
    RESPONSE=$(curl -s -X POST "http://localhost:9997/v1/models" \
        -H "Content-Type: application/json" \
        -d '{
            "model_name": "qwen2-instruct",
            "model_size_in_billions": 1.5,
            "model_format": "ggmlv3",
            "quantization": "q4_0"
        }' 2>/dev/null)
    
    if echo "$RESPONSE" | grep -q "model_id"; then
        echo -e "${GREEN}✅ 测试模型部署成功${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  模型部署可能需要时间，请稍后检查${NC}"
        return 1
    fi
}

test_model_inference() {
    echo "🧠 测试模型推理..."
    
    # 等待模型加载
    echo "⏳ 等待模型加载..."
    sleep 10
    
    RESPONSE=$(curl -s -X POST "http://localhost:9997/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -d '{
            "model": "qwen2-instruct",
            "messages": [
                {"role": "user", "content": "Hello, please respond with just OK"}
            ],
            "max_tokens": 10,
            "stream": false
        }' 2>/dev/null)
    
    if echo "$RESPONSE" | grep -q "choices"; then
        echo -e "${GREEN}✅ 模型推理测试成功${NC}"
        echo "响应: $(echo "$RESPONSE" | jq -r '.choices[0].message.content' 2>/dev/null || echo "无法解析响应")"
        return 0
    else
        echo -e "${RED}❌ 模型推理测试失败${NC}"
        echo "响应: $RESPONSE"
        return 1
    fi
}

show_integration_guide() {
    echo ""
    echo "📋 Dify 集成配置指南:"
    echo ""
    echo "1. 访问 Dify 管理后台: http://localhost"
    echo "2. 进入 '设置' -> '模型提供商'"
    echo "3. 添加自定义模型提供商:"
    echo "   - 提供商名称: Xinference"
    echo "   - API 类型: OpenAI 兼容"
    echo "   - API 端点: http://host.docker.internal:9997/v1"
    echo "   - API 密钥: sk-fake-key (任意值)"
    echo ""
    echo "4. 添加模型:"
    echo "   - 模型名称: qwen2-instruct"
    echo "   - 模型 ID: qwen2-instruct"
    echo "   - 上下文长度: 32768"
    echo ""
    echo "📖 详细说明请查看: dify_xinference_integration.md"
}

# 主测试流程
main() {
    echo "🎯 开始集成测试..."
    echo ""
    
    # 测试 Xinference 服务
    if ! test_xinference_service; then
        echo -e "${RED}请先启动 Xinference 服务: ./start_xinference.sh${NC}"
        exit 1
    fi
    
    # 测试 Dify 服务
    if ! test_dify_service; then
        echo -e "${RED}请先启动 Dify 服务: ./start.sh${NC}"
        exit 1
    fi
    
    # 测试网络连接
    test_network_connectivity
    
    # 检查是否已有模型部署
    echo "🔍 检查已部署的模型..."
    MODELS=$(curl -s http://localhost:9997/v1/models 2>/dev/null | jq -r '.data[].id' 2>/dev/null || echo "")
    
    if [ -n "$MODELS" ]; then
        echo -e "${GREEN}✅ 发现已部署的模型:${NC}"
        echo "$MODELS"
    else
        echo -e "${YELLOW}⚠️  未发现已部署的模型，尝试部署测试模型...${NC}"
        deploy_test_model
    fi
    
    # 测试模型推理
    if [ -n "$MODELS" ] || deploy_test_model; then
        test_model_inference
    fi
    
    echo ""
    echo "🎉 集成测试完成！"
    echo ""
    
    # 显示集成指南
    show_integration_guide
    
    echo ""
    echo "🔧 有用的命令:"
    echo "   查看 Xinference 状态: ./check_xinference.sh"
    echo "   查看 Dify 日志: docker-compose logs -f api"
    echo "   查看 Xinference 日志: tail -f xinference_logs/xinference.log"
}

# 运行主函数
main "$@"
